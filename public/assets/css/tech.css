.availability-cell {
    min-height: 35px;
        padding: 2px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    border: 1px solid #dee2e6;
    font-size: 12px;
}

.availability-cell.unavailable {
    background-color: #ffffff;
    border-color: #dee2e6;
}

.availability-cell.unavailable:hover {
    background-color: #f8f9fa;
}

.availability-cell.available-am {
    background: linear-gradient(to bottom, #d4edda 0%, #d4edda 50%, #ffffff 50%, #ffffff 100%);
    border-color: #c3e6cb;
}

.availability-cell.available-pm {
    background: linear-gradient(to bottom, #ffffff 0%, #ffffff 50%, #d4edda 50%, #d4edda 100%);
    border-color: #c3e6cb;
}

.availability-cell.available-full {
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.availability-cell.inactive {
    background-color: #e2e3e5;
    border-color: #d6d8db;
    cursor: not-allowed;
}

.availability-cell:hover:not(.inactive) {
    opacity: 0.8;
    transform: scale(1.02);
}

.date-col {
    min-width: 100px;
    background-color: #f8f9fa;
    font-weight: bold;
    font-size: 11px;
        padding: 3px;
}

#availability-table th {
    text-align: center;
    vertical-align: middle;
    font-size: 11px;
        padding: 3px;
}

.tech-name {
    font-weight: bold;
    background-color: #f8f9fa;
    min-width: 100px;
    cursor: pointer;
    font-size: 11px;
        padding: 3px;
}

.tech-name:hover {
    background-color: #e9ecef;
}

.tech-specialty {
    font-size: 10px;
    color: #6c757d;
    font-style: italic;
    line-height: 1.2;
    
        /* New styles for updated layout */
        .tech-name-cell {
            font-weight: bold;
            background-color: #f8f9fa;
            min-width: 100px;
            cursor: pointer;
            vertical-align: middle;
            padding: 4px;
            font-size: 11px;
            line-height: 1.3;
        }
    
        .tech-name-cell:hover {
            background-color: #e9ecef;
        }
    
        .day-header {
            background-color: #343a40;
            color: white;
            font-weight: bold;
            text-align: center;
            min-width: 25px;
            padding: 3px 2px;
            font-size: 11px;
        }
    
        .availability-cell.available {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
    
        .availability-cell.unavailable {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
    
            .availability-cell.inactive {
                background-color: #f8f9fa;
                color: #6c757d;
                border-color: #dee2e6;
                cursor: not-allowed;
            }
    
            .availability-indicator {
                font-size: 12px;
                font-weight: bold;
            }
    
            /* Responsive adjustments */
            @media (max-width: 768px) {
                .tech-name-cell {
                    min-width: 80px;
                    font-size: 10px;
                    padding: 2px;
                }
    
                .day-header {
                    min-width: 20px;
                    font-size: 10px;
                    padding: 2px 1px;
                }
    
                .availability-cell {
                    min-height: 30px;
                    padding: 1px;
                    font-size: 10px;
                }
    
                .availability-indicator {
                    font-size: 10px;
                }
    
                .table-sm td,
                .table-sm th {
                    padding: 2px;
                }
            }
    
            /* Additional compact styles */
            #availability-table {
                margin-bottom: 0;
            }
    
            #availability-table td,
            #availability-table th {
                border-width: 1px;
                line-height: 1.2;
            
                        .table-sm td,
                        .table-sm th {
                            padding: 3px;
                        }