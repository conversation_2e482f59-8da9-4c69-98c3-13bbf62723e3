<?php
require_once 'includes/db.php';
require_once 'includes/auth.php';

// Ensure user is logged in
if (!is_logged_in()) {
    header('Location: login.php');
    exit();
}

$page_title = "Technician Availability";
require_once 'includes/header.php';
?>
<link rel="stylesheet" href="assets/css/tech.css">

<div class="container-fluid mt-2">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-2">
        <h4 class="mb-0" style="font-size: 16px;">
            <i class="fas fa-user-clock me-2 text-primary"></i>
            Technician Availability
        </h4>
        <div class="btn-group" role="group">
            <a href="technicians.php" class="btn btn-secondary btn-sm">
                <i class="fas fa-user-cog me-1"></i>
                Manage Technicians
            </a>
            <a href="calendar.php" class="btn btn-primary btn-sm">
                <i class="fas fa-calendar me-1"></i>
                Calendar View
            </a>
        </div>
    </div>



    <!-- Loading Spinner -->
    <div id="loading-spinner" class="text-center py-2" style="display: none;">
        <div class="spinner-border spinner-border-sm text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-1 mb-0" style="font-size: 12px;">Loading technician availability...</p>
    </div>

    <!-- Availability Grid -->
    <div class="card">
        <div class="card-header">
            <!-- Date Navigation -->
            <div class="d-flex justify-content-between align-items-center py-2">
                <div class="d-flex align-items-center gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="prevMonth">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <h6 class="mb-0" id="monthRange" style="font-size: 14px;">Loading...</h6>
                    <button type="button" class="btn btn-outline-primary btn-sm" id="nextMonth">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                <button type="button" class="btn btn-primary btn-sm" id="todayBtn">Today</button>
            </div>
        </div>
        <div class="card-body p-2">
            <div class="table-responsive">
                <table class="table table-bordered table-sm" id="availability-table">
                    <thead id="table-header" class="table-dark">
                        <!-- Header will be generated by JavaScript -->
                    </thead>
                    <tbody id="table-body">
                        <!-- Technician rows will be added here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Legend -->
    <!-- <div class="card mt-4">
        <div class="card-body">
            <h6>Legend:</h6>
            <div class="d-flex gap-4 flex-wrap">
                <div class="d-flex align-items-center">
                    <div class="availability-cell unavailable me-2"
                        style="width: 20px; height: 20px; border: 1px solid #ddd;"></div>
                    <span>Unavailable</span>
                </div>
                <div class="d-flex align-items-center">
                    <div class="availability-cell available-am me-2"
                        style="width: 20px; height: 20px; border: 1px solid #ddd;"></div>
                    <span>Available AM</span>
                </div>
                <div class="d-flex align-items-center">
                    <div class="availability-cell available-pm me-2"
                        style="width: 20px; height: 20px; border: 1px solid #ddd;"></div>
                    <span>Available PM</span>
                </div>
                <div class="d-flex align-items-center">
                    <div class="availability-cell available-full me-2"
                        style="width: 20px; height: 20px; border: 1px solid #ddd;"></div>
                    <span>Available Full Day</span>
                </div>
                <div class="d-flex align-items-center">
                    <div class="availability-cell inactive me-2"
                        style="width: 20px; height: 20px; border: 1px solid #ddd;"></div>
                    <span>Technician Inactive</span>
                </div>
            </div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Click on cells to toggle availability. Double-click technician name to edit.
                </small>
            </div>
        </div>
    </div> -->
</div>


<script>
class TechAvailability {
    constructor() {
        this.currentMonthStart = this.getMonthStart(new Date());
        this.technicians = [];
        this.availability = {};

        this.initializeElements();
        this.bindEvents();
        this.loadData();
    }

    initializeElements() {
        this.monthRangeEl = document.getElementById('monthRange');
        this.prevMonthBtn = document.getElementById('prevMonth');
        this.nextMonthBtn = document.getElementById('nextMonth');
        this.todayBtn = document.getElementById('todayBtn');
        this.loadingSpinner = document.getElementById('loading-spinner');
        this.tableHeader = document.getElementById('table-header');
        this.tableBody = document.getElementById('table-body');
    }

    bindEvents() {
        this.prevMonthBtn.addEventListener('click', () => this.navigateMonth(-1));
        this.nextMonthBtn.addEventListener('click', () => this.navigateMonth(1));
        this.todayBtn.addEventListener('click', () => this.goToToday());
    }

    getMonthStart(date) {
        const d = new Date(date);
        return new Date(d.getFullYear(), d.getMonth(), 1);
    }

    getMonthEnd(date) {
        const d = new Date(date);
        return new Date(d.getFullYear(), d.getMonth() + 1, 0);
    }

    formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    navigateMonth(direction) {
        this.currentMonthStart.setMonth(this.currentMonthStart.getMonth() + direction);
        this.loadData();
    }

    goToToday() {
        this.currentMonthStart = this.getMonthStart(new Date());
        this.loadData();
    }

    updateMonthRange() {
        const options = {
            year: 'numeric',
            month: 'long'
        };
        this.monthRangeEl.textContent = this.currentMonthStart.toLocaleDateString('en-US', options);
    }

    async loadData() {
        this.showLoading(true);

        try {
            // Load technicians
            const techsData = await apiRequest('techs', 'list');

            if (!techsData.success) {
                throw new Error(techsData.error || 'Failed to load technicians');
            }

            this.technicians = techsData.technicians;

            // Load availability for the month
            const startDate = this.formatDate(this.currentMonthStart);
            const endDate = this.formatDate(this.getMonthEnd(this.currentMonthStart));

            const availData = await apiRequest('techAvail', 'byRange', {
                start: startDate,
                end: endDate
            });

            if (!availData.success) {
                throw new Error(availData.error || 'Failed to load availability');
            }

            // Process availability data
            this.availability = {};
            availData.availability.forEach(avail => {
                const key = `${avail.tech_id}-${avail.date}`;
                if (!this.availability[key]) {
                    this.availability[key] = [];
                }
                this.availability[key].push(avail.period);
            });

            this.render();

        } catch (error) {
            console.error('Error loading data:', error);
            this.showError('Failed to load technician availability data');
        } finally {
            this.showLoading(false);
        }
    }

    render() {
        this.updateMonthRange();
        this.renderTable();
    }

    renderTable() {
        // Clear existing content
        this.tableHeader.innerHTML = '';
        this.tableBody.innerHTML = '';

        // Generate header row with dates
        const headerRow = document.createElement('tr');
        const techHeader = document.createElement('th');
        techHeader.className = 'tech-name';
        techHeader.textContent = 'Technician';
        headerRow.appendChild(techHeader);

        // Generate all days of the month for header
        const monthStart = new Date(this.currentMonthStart);
        const monthEnd = this.getMonthEnd(this.currentMonthStart);
        const dates = [];
        let currentDate = new Date(monthStart);

        while (currentDate <= monthEnd) {
            dates.push(new Date(currentDate));
            currentDate.setDate(currentDate.getDate() + 1);
        }

        // Add day numbers to header
        dates.forEach(date => {
            const dayHeader = document.createElement('th');
            dayHeader.className = 'day-header';
            dayHeader.textContent = date.getDate();
            headerRow.appendChild(dayHeader);
        });
        this.tableHeader.appendChild(headerRow);

        // Generate rows for each technician
        this.technicians.forEach(tech => {
            const row = document.createElement('tr');

            // Technician name cell
            const techCell = document.createElement('td');
            techCell.className = 'tech-name-cell';
            techCell.innerHTML = `
                <strong>${this.escapeHtml(tech.name)}</strong>
                ${tech.specialty ? `<br><span class="tech-specialty">${this.escapeHtml(tech.specialty)}</span>` : ''}
            `;
            techCell.addEventListener('dblclick', () => this.editTechnician(tech.id));
            row.appendChild(techCell);

            // Availability cells for each day
            dates.forEach(date => {
                const dateStr = this.formatDate(date);
                const cell = document.createElement('td');
                cell.className = 'availability-cell';

                if (!tech.is_active) {
                    cell.classList.add('inactive');
                    cell.innerHTML = '<small>-</small>';
                } else {
                    const availKey = `${tech.id}-${dateStr}`;
                    const isAvailable = this.availability[availKey] && this.availability[availKey]
                        .length > 0;

                    this.setCellAvailability(cell, isAvailable, tech.id, dateStr);
                }

                row.appendChild(cell);
            });

            this.tableBody.appendChild(row);
        });
    }

    setCellAvailability(cell, isAvailable, techId, date) {
        cell.innerHTML = '';
        cell.className = 'availability-cell'; // Reset classes
        cell.style.textAlign = 'center';
        cell.style.cursor = 'pointer';

        if (isAvailable) {
            cell.classList.add('available');
            cell.innerHTML = '<span class="availability-indicator">✓</span>';
            cell.style.backgroundColor = '#d4edda';
            cell.style.color = '#155724';
        } else {
            cell.classList.add('unavailable');
            cell.innerHTML = '<span class="availability-indicator">✗</span>';
            cell.style.backgroundColor = '#f8d7da';
            cell.style.color = '#721c24';
        }

        // Add click handler for simple toggle
        if (!cell.classList.contains('inactive')) {
            cell.addEventListener('click', async () => {
                await this.toggleAvailability(techId, date, isAvailable);
            });
        }
    }
    async toggleAvailability(techId, date, currentlyAvailable) {
        try {
            const availKey = `${techId}-${date}`;

            if (currentlyAvailable) {
                // Remove availability - find and delete all periods for this date
                const availIds = await this.findAllAvailabilityIds(techId, date);
                for (const availId of availIds) {
                    const response = await fetch('api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        body: `entity=techAvail&action=unset&id=${availId}`
                    });

                    const result = await response.json();
                    if (!result.success) {
                        this.showError(result.error || 'Failed to remove availability');
                        return;
                    }
                }
            } else {
                // Add availability - set as full day
                const formData = new FormData();
                formData.append('entity', 'techAvail');
                formData.append('action', 'set');
                formData.append('tech_id', techId);
                formData.append('date', date);
                formData.append('period', 'full');

                const response = await fetch('api.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                if (!result.success) {
                    this.showError(result.error || 'Failed to set availability');
                    return;
                }
            }

            this.loadData(); // Reload to refresh display
        } catch (error) {
            console.error('Error toggling availability:', error);
            this.showError('Failed to update availability');
        }
    }

    async findAllAvailabilityIds(techId, date) {
        try {
            const result = await apiRequest('techAvail', 'list', {
                tech_id: techId,
                date: date
            });

            if (result.success) {
                return result.availability.map(a => a.id);
            }
        } catch (error) {
            console.error('Error finding availability IDs:', error);
        }
        return [];
    }

    editTechnician(techId) {
        // Redirect to technician management page with edit mode
        window.location.href = `technicians.php#edit-${techId}`;
    }

    showLoading(show) {
        this.loadingSpinner.style.display = show ? 'block' : 'none';
    }

    showError(message) {
        // Simple error display - you can enhance this
        alert(message);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new TechAvailability();
});
</script>

<?php require_once 'includes/footer.php'; ?>